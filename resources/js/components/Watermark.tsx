import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
  generateWatermarkStyles,
  calculateWatermarkRepetition,
  type WatermarkRepeatType
} from '@/utils/watermark-utils';

interface WatermarkProps {
  enabled?: boolean;
  logoUrl?: string;
  text?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  opacity?: number;
  size?: 'small' | 'medium' | 'large' | 'xl' | 'xxl' | 'custom';
  customWidth?: number;
  customHeight?: number;
  offsetX?: number;
  offsetY?: number;
  repeat?: WatermarkRepeatType;
  showForUserType?: boolean;
  className?: string;
}

interface WatermarkConfig {
  enabled: boolean;
  logo_url: string;
  text: string;
  position: string;
  opacity: number;
  size: string;
  custom_width: number;
  custom_height: number;
  offset_x: number;
  offset_y: number;
  repeat: string;
  show_for_user: boolean;
}

export default function Watermark({
  enabled = true,
  logoUrl,
  text = 'Mobile Parts DB',
  position = 'bottom-right',
  opacity = 0.3,
  size = 'medium',
  customWidth = 120,
  customHeight = 40,
  offsetX = 16,
  offsetY = 16,
  repeat = 'single',
  showForUserType = true,
  className,
}: WatermarkProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Don't render if disabled or not for this user type
  if (!enabled || !showForUserType) {
    return null;
  }

  // Calculate styles using utility functions
  const containerStyles = generateWatermarkStyles(
    position as any,
    size as any,
    opacity,
    offsetX,
    offsetY,
    customWidth,
    customHeight,
    repeat
  );

  // Calculate repetition settings
  const repetitionConfig = calculateWatermarkRepetition(
    repeat,
    size as any,
    customWidth,
    customHeight
  );

  // Handle image load error
  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // Handle image load success
  const handleImageLoad = () => {
    setImageError(false);
    setImageLoaded(true);
  };

  // Reset image error when logoUrl changes
  useEffect(() => {
    if (logoUrl) {
      setImageError(false);
      setImageLoaded(false);
    }
  }, [logoUrl]);

  // Determine what to render
  const shouldShowImage = logoUrl && !imageError;
  const shouldShowText = !logoUrl || imageError;

  // Render single watermark item
  const renderWatermarkItem = (key?: number) => (
    <div
      key={key}
      className={cn(
        'watermark-item',
        'transition-opacity duration-300'
      )}
      style={{
        ...repetitionConfig.itemStyles,
        // Ensure opacity is applied to individual items for repeated modes
        ...(repeat !== 'single' && { opacity })
      }}
    >
      {shouldShowImage && (
        <img
          src={logoUrl}
          alt={text}
          className={cn(
            'w-full h-full object-contain',
            'transition-opacity duration-300',
            imageLoaded ? 'opacity-100' : 'opacity-0'
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
          draggable={false}
        />
      )}

      {shouldShowText && (
        <div
          className={cn(
            'flex items-center justify-center',
            'w-full h-full',
            'text-gray-600 font-medium',
            'bg-white/80 rounded-sm',
            'border border-gray-200',
            'transition-opacity duration-300',
            size === 'small' && 'text-xs px-2',
            size === 'medium' && 'text-sm px-3',
            size === 'large' && 'text-base px-4',
            size === 'xl' && 'text-lg px-5',
            size === 'xxl' && 'text-xl px-6',
            size === 'custom' && 'text-sm px-2'
          )}
        >
          {text}
        </div>
      )}
    </div>
  );

  return (
    <div
      className={cn(
        'watermark-container',
        'transition-opacity duration-300',
        className
      )}
      style={containerStyles}
    >
      {repeat === 'single' ? (
        renderWatermarkItem()
      ) : (
        <div style={repetitionConfig.containerStyles}>
          {Array.from({ length: repetitionConfig.count }, (_, i) =>
            renderWatermarkItem(i)
          )}
        </div>
      )}
    </div>
  );
}

// Hook to fetch watermark configuration from backend
export function useWatermarkConfig() {
  const [config, setConfig] = useState<WatermarkConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/watermark-config');
        if (!response.ok) {
          throw new Error('Failed to fetch watermark configuration');
        }
        const data = await response.json();
        setConfig(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        // Set default config on error
        setConfig({
          enabled: false,
          logo_url: '',
          text: 'Mobile Parts DB',
          position: 'bottom-right',
          opacity: 0.3,
          size: 'medium',
          custom_width: 120,
          custom_height: 40,
          offset_x: 16,
          offset_y: 16,
          show_for_user: false,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return { config, loading, error };
}

// Wrapper component that automatically fetches configuration
export function AutoWatermark({ className }: { className?: string }) {
  const { config, loading } = useWatermarkConfig();

  if (loading || !config) {
    return null;
  }

  return (
    <Watermark
      enabled={config.enabled}
      logoUrl={config.logo_url}
      text={config.text}
      position={config.position as any}
      opacity={config.opacity}
      size={config.size as any}
      customWidth={config.custom_width}
      customHeight={config.custom_height}
      offsetX={config.offset_x}
      offsetY={config.offset_y}
      repeat={config.repeat as WatermarkRepeatType}
      showForUserType={config.show_for_user}
      className={className}
    />
  );
}

// Utility function to wrap components with watermark
export function withWatermark<T extends object>(
  Component: React.ComponentType<T>,
  watermarkProps?: Partial<WatermarkProps>
) {
  return function WatermarkedComponent(props: T) {
    return (
      <div className="relative">
        <Component {...props} />
        <AutoWatermark {...watermarkProps} />
      </div>
    );
  };
}
