import { Breadcrumbs } from '@/components/breadcrumbs';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { Link, router } from '@inertiajs/react';
import {
    Search,
    Plus,
    Settings,
    HelpCircle,
    Zap,
    Moon,
    Sun,
    Monitor,
    Shield,
    LayoutGrid,
    Globe
} from 'lucide-react';
import NotificationBell from '@/components/user/NotificationBell';
import { useAppearance } from '@/hooks/use-appearance';
import { useAdmin } from '@/hooks/use-admin';
import { useState } from 'react';
import { toast } from 'sonner';

export function AppSidebarHeader({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    const { appearance, setAppearance } = useAppearance();
    const isAdmin = useAdmin();
    const [isClearingCache, setIsClearingCache] = useState(false);

    const handleClearCache = async () => {
        if (isClearingCache) return;

        setIsClearingCache(true);

        try {
            router.post('/admin/dashboard/clear-cache', {}, {
                onSuccess: (page) => {
                    // Check for success message in flash data
                    const successMessage = (page.props as any).flash?.success;
                    if (successMessage) {
                        toast.success(successMessage);
                    } else {
                        toast.success('All caches cleared successfully!');
                    }
                },
                onError: (errors) => {
                    // Check for error message in flash data or use default
                    const errorMessage = errors?.message || 'Failed to clear caches. Please try again.';
                    toast.error(errorMessage);
                },
                onFinish: () => {
                    setIsClearingCache(false);
                }
            });
        } catch (error) {
            toast.error('An error occurred while clearing caches.');
            setIsClearingCache(false);
        }
    };

    const quickActions = [
        {
            title: 'Search Parts',
            href: '/search',
            icon: Search,
            description: 'Find mobile parts quickly',
            shortcut: '⌘K'
        },
        {
            title: 'Add New Part',
            href: '/admin/parts/create',
            icon: Plus,
            description: 'Create a new part entry',
            adminOnly: true
        },
        {
            title: 'Dashboard',
            href: isAdmin ? '/admin/dashboard' : '/dashboard',
            icon: LayoutGrid,
            description: 'Go to main dashboard'
        },
        {
            title: 'Settings',
            href: '/settings/profile',
            icon: Settings,
            description: 'Manage your account'
        },
    ];

    const themeOptions = [
        { value: 'light', label: 'Light', icon: Sun },
        { value: 'dark', label: 'Dark', icon: Moon },
        { value: 'system', label: 'System', icon: Monitor },
    ];

    return (
        <header className="flex h-16 shrink-0 items-center gap-3 border-b border-sidebar-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4">
            {/* Left Section - Hamburger Menu and Breadcrumbs */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="h-6" />
                <div className="flex-1 min-w-0">
                    <Breadcrumbs breadcrumbs={breadcrumbs} />
                </div>
            </div>

            {/* Right Section - Action Buttons */}
            <div className="flex items-center gap-2">
                {/* Quick Search Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="hidden md:flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors"
                    asChild
                >
                    <Link href="/search">
                        <Search className="h-4 w-4" />
                        <span className="text-sm">Search</span>
                        <Badge variant="outline" className="ml-1 px-1.5 py-0.5 text-xs font-mono">
                            ⌘K
                        </Badge>
                    </Link>
                </Button>

                {/* Visit Site Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors hover:bg-teal-50 hover:text-teal-700 dark:hover:bg-teal-950 dark:hover:text-teal-300"
                    asChild
                >
                    <a href="/" target="_blank" rel="noopener noreferrer">
                        <Globe className="h-4 w-4" />
                        <span className="text-sm hidden sm:inline">Visit Site</span>
                    </a>
                </Button>

                {/* Quick Actions Dropdown */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            <Plus className="h-4 w-4" />
                            <span className="sr-only">Quick Actions</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64">
                        <DropdownMenuLabel className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            Quick Actions
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {quickActions.map((action) => {
                            if (action.adminOnly && !isAdmin) return null;
                            return (
                                <DropdownMenuItem key={action.href} asChild>
                                    <Link href={action.href} className="flex items-center gap-3 p-2">
                                        <action.icon className="h-4 w-4 text-muted-foreground" />
                                        <div className="flex-1">
                                            <div className="font-medium">{action.title}</div>
                                            <div className="text-xs text-muted-foreground">{action.description}</div>
                                        </div>
                                        {action.shortcut && (
                                            <Badge variant="outline" className="text-xs font-mono">
                                                {action.shortcut}
                                            </Badge>
                                        )}
                                    </Link>
                                </DropdownMenuItem>
                            );
                        })}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Theme Switcher */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            {appearance === 'light' ? (
                                <Sun className="h-4 w-4" />
                            ) : appearance === 'dark' ? (
                                <Moon className="h-4 w-4" />
                            ) : (
                                <Monitor className="h-4 w-4" />
                            )}
                            <span className="sr-only">Toggle theme</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Theme</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {themeOptions.map((theme) => (
                            <DropdownMenuItem
                                key={theme.value}
                                onSelect={() => {
                                    console.log('Theme selected:', theme.value); // Debug log
                                    setAppearance(theme.value as any);
                                }}
                                className="flex items-center gap-2"
                            >
                                <theme.icon className="h-4 w-4" />
                                {theme.label}
                                {appearance === theme.value && (
                                    <Badge variant="outline" className="ml-auto text-xs">
                                        Active
                                    </Badge>
                                )}
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Clear Cache Button - Admin Only */}
                {isAdmin && (
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-950"
                        title="Clear All Caches"
                        onClick={handleClearCache}
                        disabled={isClearingCache}
                    >
                        <div className={`h-4 w-4 text-red-600 dark:text-red-400 ${isClearingCache ? 'animate-pulse' : ''}`}>
                            <svg viewBox="0 0 730 730" className="w-full h-full">
                            <defs>
                                <style>
                                {`
                                    .st0 { fill: currentColor; }
                                    .st1 { fill: none; stroke: currentColor; stroke-miterlimit: 10; stroke-width: 25px; }
                                    .st2 { fill: currentColor; }
                                `}
                                </style>
                            </defs>
                            <g>
                                <path className="st0" d="M358.8,358.1c4.1.6,7.1,1.9,10.7,4,1.1.6,2.2,1.2,3.3,1.9,1.2.7,2.3,1.3,3.5,2,1.2.7,2.4,1.4,3.6,2,3.6,2,7.3,4.1,10.9,6.2,1.1.6,2.2,1.2,3.3,1.9,5.9,3.4,11.8,6.7,17.7,10.1-.6,3.5-2.1,5.9-4.1,8.8-.6.9-1.3,1.8-1.9,2.8q-1,1.5-2,2.9t-2.1,3c-21.2,30.6-44.9,53.8-77.9,71.5-.7.4-1.4.7-2,1.1-4.6,2.4-9.2,4.7-14,6.9q-1.1.5-2.3,1.1c-21.7,10-44.4,16.9-67.7,21.9,3.4,3.6,6.9,6.6,10.9,9.6,1,.7,2,1.5,3,2.3q1.5,1.1,3,2.2c1,.8,1.9,1.6,2.9,2.4q3,2,6.6,1.2c1.3-.4,2.6-.9,3.9-1.3.7-.2,1.4-.5,2.1-.7,18.6-6.7,32.6-17.4,46.8-30.9,2.5-2.4,5.1-4.7,7.6-7,.8-.7,1.6-1.4,2.4-2.1q1.8-1.5,2.8-1.5c-1.8,24.5-7.3,42.1-22.3,61.5-2.1,2.7-4,5.5-5.7,8.5,1.2.3,2.4.7,3.6,1,3.9,1.3,7.2,3,10.8,5.1,7.6,4.4,13.7,6.6,22.7,4.9,23.9-6.5,44.9-23.7,63.3-39.5,1.8-1.5,3.5-3,5.3-4.5,7.7-6.6,15.3-13.3,22.7-20.1.7-.6,1.4-1.3,2.2-2,.6-.6,1.3-1.1,1.9-1.7q1.6-1.2,3.6-1.2c-1.6,17.7-6.8,37.8-15.9,53.2-1.2,2-2.3,4.1-3.4,6.2-5.9,11-12.6,21.4-19.7,31.7,10.6,2.7,21.2,4.4,32,6,.2-.7.4-1.3.7-2,1.7-4,4.1-7.4,6.4-11,23-35.9,40.5-74.9,54.8-115,.3-.9.7-1.9,1-2.8,1.7-4.7,3.3-9.4,5-14.1.6-1.7,1.2-3.4,1.8-5.1q.4-1.2.8-2.4.8-2.2,2.4-5.6c3.1-1.4,5.2-.9,8.4.2.9.3,1.7.6,2.6.9.9.3,1.8.6,2.8,1,.9.3,1.9.6,2.9,1,2,.7,4,1.4,6,2.1,3,1.1,6.1,2.1,9.2,3.2,2,.7,3.9,1.3,5.9,2,.9.3,1.8.6,2.7.9,5.7,2,11.2,4.2,16.6,6.8-.6,5.1-1.7,9.7-3.4,14.6-.2.7-.5,1.4-.8,2.2-.8,2.4-1.7,4.7-2.5,7.1-.3.8-.6,1.6-.9,2.5-9.4,26.5-20.3,52.4-32.4,77.7-.5,1-1,2-1.5,3.1-7.4,15.4-15.7,30.3-24.5,44.9q-.9,1.5-1.9,3.1-24.8,41-39.4,46c-52.9,12.4-133.6-22.6-178.7-46.1-2.2-1.1-4.3-2.2-6.5-3.3-36.7-18.7-69.2-44.4-98.3-73.4-.7-.7-1.4-1.4-2.1-2.1-4.6-4.6-8.9-9.3-13-14.2q-1.1-1.3-2.3-2.7c-8.5-10.3-11.7-18.8-10.7-32.3,2.2-9.1,6.9-15,14.7-19.9,7.4-3.6,15-3.5,23.1-4,51.8-3,107.7-17.9,147.2-53.1.5-.5,1-.9,1.6-1.4,8.1-7.2,14-15.6,20-24.5,1.8-2.7,3.6-5.3,5.5-7.9,1.7-2.4,3.3-4.8,5-7.2Z"/>
                                <path className="st2" d="M579.8,141.1c7.4,4.6,14.1,10.6,17,19,1.9,12.3-2.5,22.7-6.8,33.9-.7,2-1.4,3.9-2.2,5.9-1.5,4.2-3.1,8.4-4.7,12.6-2.5,6.7-4.9,13.3-7.3,20-3.8,10.4-7.5,20.8-11.3,31.1-2.6,7-5.1,14-7.6,21-1.6,4.5-3.3,9-4.9,13.5-.8,2.1-1.5,4.2-2.3,6.4-1.1,2.9-2.1,5.9-3.2,8.8-.3.8-.6,1.7-.9,2.6-2.2,6-4.9,11.6-7.8,17.2-5.6-.6-10.1-2.6-15.1-4.9-.9-.4-1.8-.8-2.7-1.2-2.8-1.3-5.6-2.6-8.4-3.9-2.7-1.3-5.5-2.5-8.2-3.8-1.8-.8-3.6-1.7-5.5-2.5-4.6-2.1-9.1-4.2-13.7-6.1-.9-.4-1.8-.8-2.7-1.2-1.7-.7-3.4-1.4-5-2.1-5.9-2.6-12.7-6.3-15.7-12.3,4.5-13.4,12.1-25.8,18.9-38.2,3.6-6.6,7.1-13.2,10.6-19.8,6.3-11.8,12.6-23.6,18.9-35.4,3.4-6.3,6.8-12.7,10.2-19.1.4-.7.8-1.4,1.1-2.2,1.8-3.5,3.7-6.9,5.5-10.4,12.1-22.9,24.6-43.3,53.9-29Z"/>
                                <path className="st0" d="M237,321.4c10.8,10.1,18.3,23.1,19,38.1.5,17.2-3.1,31.6-14.7,44.9-11.9,12.4-25.3,18.7-42.5,19.6-15.9.2-30.5-5.6-41.9-16.8-.7-.7-1.4-1.5-2.2-2.2-.6-.6-1.3-1.3-2-1.9-9.6-10-13.9-24.5-14.4-38.1.7-16.9,7.3-31.3,19.4-43,.5-.6,1.1-1.1,1.6-1.7,21.6-20.8,56.2-17.5,77.6,1.1Z"/>
                                <path className="st0" d="M552.6,349q-.8-.7-1.5-1.5-7.5-7.1-30.8-17.7c-.7-.3-1.5-.7-2.2-1-4.7-2.2-9.5-4.3-14.2-6.4-2.6-1.2-5.2-2.3-7.8-3.5-4.6-2.1-9.1-4.1-13.7-6.1-1.8-.8-3.6-1.6-5.4-2.4-13.9-6.3-27.6-12.8-43.1-12.7-.9,0-1.8,0-2.7,0-6.3,0-11.9.4-18,2-8.7,2.7-16.3,6.5-23,11.5-.1,0-.3-.1-.4-.2-2.7,2-5.1,4.1-7.5,6.5-.8.9-1.6,1.7-2.4,2.6-1.9,2-3.2,3.1-3.8,4.9-2.9,3.7-5.5,7.8-7.9,12.3-.4.7-.7,1.4-1.1,2.2-1.6,3.1-3.5,6-3.4,9.6q.9,2,3.3,3.4c1.1.5,2.2.9,3.2,1.3,1.2.5,2.5,1.1,3.7,1.6,3.6,1.5,7.1,3,10.7,4.5.8.3,1.5.7,2.3,1,8.4,3.6,16.6,7.3,24.9,11,15.8,7.2,31.6,14.3,47.4,21.2,12.7,5.5,25.3,11.1,37.9,16.8,13.2,6,26.4,11.9,39.6,17.7,1.8.8,3.7,1.6,5.5,2.4,2.6,1.2,5.2,2.3,7.9,3.5.8.4,1.6.7,2.4,1.1,4.3,1.9,8.4,3.7,13.1,4.4,4.9-12.7,9.4-25.6,9.4-39.4-.6-20.3-7.9-36.7-22.5-50.7Z"/>
                                <path className="st0" d="M237.4,254.8c-1.9-8.7-6.4-14.8-13.6-19.8-9.2-6.1-22.1-6.2-31.7-.9,0,0-.2,0-.3-.1-2.6,1.7-2.9,2-4.1,3.1-.3.3-.7.6-1,.9-1.3,1.4-2.4,2.7-3.3,3.9-.2.2-.3.4-.4.6-4.7,6.5-5.6,12.4-5.4,21.9.6,7.8,3.6,13.3,9.3,18.6,7.4,6.1,14.6,8.5,24.2,7.8,8.8-1.2,15-5.2,20.5-11.9,5.5-7.7,7-14.8,5.9-24.2Z"/>
                                <path className="st0" d="M160.9,244.1c-.9-10-6.9-18.5-14.4-24.9-14.5-10.3-33.2-10.2-47.5-1,0,0-.2,0-.3-.1l-.9.9c-2.2,1.5-4.2,3.2-6.1,5.1-7,8.3-10.1,18.1-10,28.9,1,12.1,5.8,21.1,15,29,8.7,6.8,18.8,9.6,29.8,8.8,12.5-1.8,20.8-7.9,28.5-17.7q.9-1.6,1.8-3.1c4.7-8.3,4.7-16.7,4.2-26Z"/>
                            </g>
                            <circle className="st1" cx="364.9" cy="365" r="349.5"/>
                            </svg>
                        </div>
                        <span className="sr-only">Clear All Caches</span>
                    </Button>
                )}

                {/* Help Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                    title="Help & Support"
                >
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Help</span>
                </Button>

                <Separator orientation="vertical" className="h-6" />

                {/* Notifications */}
                <NotificationBell />

                {/* Admin Badge */}
                {isAdmin && (
                    <Badge variant="outline" className="hidden sm:flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-amber-950 dark:text-orange-300 dark:border-orange-800">
                        <Shield className="h-3 w-3" />
                        <span className="text-xs font-medium">Admin</span>
                    </Badge>
                )}
            </div>
        </header>
    );
}
