import { Breadcrumbs } from '@/components/breadcrumbs';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { Link, router } from '@inertiajs/react';
import {
    Search,
    Plus,
    Settings,
    HelpCircle,
    Zap,
    Moon,
    Sun,
    Monitor,
    Shield,
    LayoutGrid,
    Globe
} from 'lucide-react';
import NotificationBell from '@/components/user/NotificationBell';
import { useAppearance } from '@/hooks/use-appearance';
import { useAdmin } from '@/hooks/use-admin';
import { useState } from 'react';
import { toast } from 'sonner';

export function AppSidebarHeader({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    const { appearance, setAppearance } = useAppearance();
    const isAdmin = useAdmin();
    const [isClearingCache, setIsClearingCache] = useState(false);

    const handleClearCache = async () => {
        if (isClearingCache) return;

        setIsClearingCache(true);

        try {
            router.post('/admin/dashboard/clear-cache', {}, {
                onSuccess: () => {
                    // Flash message will be handled automatically by FlashMessageHandler
                    // No need to manually show toast here
                },
                onError: (errors) => {
                    // Handle client-side errors that don't have flash messages
                    const errorMessage = errors?.message || 'Failed to clear caches. Please try again.';
                    toast.error(errorMessage);
                },
                onFinish: () => {
                    setIsClearingCache(false);
                }
            });
        } catch (error) {
            toast.error('An error occurred while clearing caches.');
            setIsClearingCache(false);
        }
    };

    const quickActions = [
        {
            title: 'Search Parts',
            href: '/search',
            icon: Search,
            description: 'Find mobile parts quickly',
            shortcut: '⌘K'
        },
        {
            title: 'Add New Part',
            href: '/admin/parts/create',
            icon: Plus,
            description: 'Create a new part entry',
            adminOnly: true
        },
        {
            title: 'Dashboard',
            href: isAdmin ? '/admin/dashboard' : '/dashboard',
            icon: LayoutGrid,
            description: 'Go to main dashboard'
        },
        {
            title: 'Settings',
            href: '/settings/profile',
            icon: Settings,
            description: 'Manage your account'
        },
    ];

    const themeOptions = [
        { value: 'light', label: 'Light', icon: Sun },
        { value: 'dark', label: 'Dark', icon: Moon },
        { value: 'system', label: 'System', icon: Monitor },
    ];

    return (
        <header className="flex h-16 shrink-0 items-center gap-3 border-b border-sidebar-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4">
            {/* Left Section - Hamburger Menu and Breadcrumbs */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="h-6" />
                <div className="flex-1 min-w-0">
                    <Breadcrumbs breadcrumbs={breadcrumbs} />
                </div>
            </div>

            {/* Right Section - Action Buttons */}
            <div className="flex items-center gap-2">
                {/* Quick Search Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="hidden md:flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors"
                    asChild
                >
                    <Link href="/search">
                        <Search className="h-4 w-4" />
                        <span className="text-sm">Search</span>
                        <Badge variant="outline" className="ml-1 px-1.5 py-0.5 text-xs font-mono">
                            ⌘K
                        </Badge>
                    </Link>
                </Button>

                {/* Visit Site Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-2 h-8 px-3 text-muted-foreground hover:text-foreground transition-colors hover:bg-teal-50 hover:text-teal-700 dark:hover:bg-teal-950 dark:hover:text-teal-300"
                    asChild
                >
                    <a href="/" target="_blank" rel="noopener noreferrer">
                        <Globe className="h-4 w-4" />
                        <span className="text-sm hidden sm:inline">Visit Site</span>
                    </a>
                </Button>

                {/* Quick Actions Dropdown */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            <Plus className="h-4 w-4" />
                            <span className="sr-only">Quick Actions</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64">
                        <DropdownMenuLabel className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            Quick Actions
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {quickActions.map((action) => {
                            if (action.adminOnly && !isAdmin) return null;
                            return (
                                <DropdownMenuItem key={action.href} asChild>
                                    <Link href={action.href} className="flex items-center gap-3 p-2">
                                        <action.icon className="h-4 w-4 text-muted-foreground" />
                                        <div className="flex-1">
                                            <div className="font-medium">{action.title}</div>
                                            <div className="text-xs text-muted-foreground">{action.description}</div>
                                        </div>
                                        {action.shortcut && (
                                            <Badge variant="outline" className="text-xs font-mono">
                                                {action.shortcut}
                                            </Badge>
                                        )}
                                    </Link>
                                </DropdownMenuItem>
                            );
                        })}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Theme Switcher */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                        >
                            {appearance === 'light' ? (
                                <Sun className="h-4 w-4" />
                            ) : appearance === 'dark' ? (
                                <Moon className="h-4 w-4" />
                            ) : (
                                <Monitor className="h-4 w-4" />
                            )}
                            <span className="sr-only">Toggle theme</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Theme</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {themeOptions.map((theme) => (
                            <DropdownMenuItem
                                key={theme.value}
                                onSelect={() => {
                                    console.log('Theme selected:', theme.value); // Debug log
                                    setAppearance(theme.value as any);
                                }}
                                className="flex items-center gap-2"
                            >
                                <theme.icon className="h-4 w-4" />
                                {theme.label}
                                {appearance === theme.value && (
                                    <Badge variant="outline" className="ml-auto text-xs">
                                        Active
                                    </Badge>
                                )}
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Clear Cache Button - Admin Only */}
                {isAdmin && (
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-950"
                        title="Clear All Caches"
                        onClick={handleClearCache}
                        disabled={isClearingCache}
                    >
                        <div className={`h-4 w-4 text-red-600 dark:text-red-400 ${isClearingCache ? 'animate-pulse' : ''}`}>
                            <svg viewBox="0 0 730 730" className="w-full h-full">
                            <defs>
                                <style>
                                {`
                                    .st0 { fill: currentColor; }
                                    .st1 { fill: none; stroke: currentColor; stroke-miterlimit: 10; stroke-width: 25px; }
                                    .st2 { fill: currentColor; }
                                `}
                                </style>
                            </defs>       
                                <path class="st0" d="M312.3,314.1c5.7.8,9.9,2.7,14.9,5.5,1.5.9,3.1,1.7,4.6,2.6,1.6.9,3.3,1.9,4.9,2.8,1.7.9,3.4,1.9,5,2.8,5.1,2.8,10.1,5.7,15.2,8.6,1.5.9,3.1,1.7,4.6,2.6,8.2,4.7,16.5,9.4,24.7,14.1-.9,4.9-2.9,8.2-5.7,12.2-.9,1.3-1.7,2.6-2.7,3.9q-1.4,2-2.8,4.1t-2.9,4.2c-29.6,42.7-62.6,75-108.7,99.7-.9.5-1.9,1-2.9,1.5-6.4,3.4-12.9,6.6-19.5,9.6q-1.6.7-3.2,1.5c-30.2,14-62,23.6-94.4,30.6,4.8,5,9.7,9.2,15.3,13.3,1.4,1,2.8,2.1,4.2,3.1q2.1,1.5,4.2,3c1.3,1.1,2.7,2.2,4.1,3.3q4.2,2.7,9.2,1.7c1.8-.6,3.6-1.2,5.4-1.9,1-.3,1.9-.7,2.9-1,25.9-9.3,45.5-24.2,65.2-43,3.5-3.3,7.1-6.6,10.7-9.8,1.1-1,2.2-2,3.3-3q2.5-2.1,3.8-2.1c-2.5,34.1-10.2,58.8-31.1,85.7-2.9,3.8-5.5,7.7-7.9,11.9,1.6.5,3.3.9,5,1.4,5.5,1.8,10.1,4.2,15,7.1,10.6,6.2,19.1,9.2,31.6,6.9,33.3-9.1,62.6-33,88.3-55.1,2.4-2.1,4.9-4.2,7.4-6.3,10.7-9.1,21.3-18.5,31.7-28,1-.9,2-1.8,3-2.7.9-.8,1.8-1.6,2.7-2.4q2.2-1.7,5-1.7c-2.2,24.7-9.5,52.7-22.2,74.1-1.7,2.8-3.2,5.7-4.7,8.6-8.2,15.4-17.6,29.8-27.5,44.2,14.7,3.7,29.6,6.1,44.6,8.4.3-.9.6-1.8.9-2.8,2.4-5.5,5.7-10.3,9-15.3,32-50,56.5-104.5,76.4-160.3.5-1.3.9-2.6,1.4-4,2.3-6.5,4.7-13.1,6.9-19.6.8-2.4,1.7-4.8,2.5-7.2q.6-1.7,1.1-3.4,1.1-3.1,3.4-7.8c4.4-1.9,7.2-1.2,11.6.3,1.2.4,2.4.8,3.6,1.2,1.3.4,2.5.9,3.9,1.4,1.3.5,2.6.9,4,1.4,2.8,1,5.6,1.9,8.4,2.9,4.3,1.5,8.5,2.9,12.8,4.4,2.7.9,5.5,1.9,8.2,2.8,1.3.4,2.5.9,3.8,1.3,7.9,2.8,15.6,5.9,23.2,9.4-.8,7.1-2.4,13.6-4.8,20.3-.3,1-.7,2-1.1,3-1.1,3.3-2.3,6.6-3.5,9.8-.4,1.1-.8,2.3-1.2,3.4-13.1,37-28.3,73-45.2,108.4-.7,1.4-1.3,2.8-2,4.3-10.4,21.5-21.9,42.2-34.2,62.7q-1.3,2.1-2.6,4.3-34.5,57.2-55,64.2c-73.7,17.3-186.4-31.6-249.2-64.3-3-1.6-6-3.1-9.1-4.6-51.2-26.1-96.5-61.9-137.1-102.4-1-1-2-2-3-3-6.3-6.4-12.4-12.9-18.2-19.8q-1.6-1.9-3.2-3.7c-11.8-14.4-16.4-26.3-14.9-45.1,3.1-12.7,9.6-20.9,20.6-27.8,10.4-5,20.9-4.9,32.2-5.5,72.3-4.2,150.2-24.9,205.3-74.1.7-.6,1.4-1.3,2.2-1.9,11.2-10,19.5-21.8,27.9-34.2,2.5-3.7,5.1-7.4,7.6-11.1,2.3-3.3,4.6-6.7,6.9-10ZM322.1,354.6c-1.9,2.6-3.7,5.3-5.5,8-41.2,61.4-118.7,91.4-188.1,106-24.7,4.9-50.1,7.5-75.3,8.3-4.1.4-6.5.9-10,3.2-2.6,3.5-2.8,5.2-2.5,9.5,1.3,5.1,3,7.5,6.4,11.5q1.7,2,3.5,4.1c1.3,1.4,2.5,2.8,3.8,4.2,1.2,1.4,2.4,2.7,3.7,4.1,15.5,17.2,31.7,34.6,50,48.9,3.9,3,7.6,6.3,11.4,9.5,25.7,21.4,54.7,39,84.2,54.6,1.1.6,2.3,1.2,3.5,1.8,59.1,31.1,123.2,50.4,189,60.9,1.1.2,2.3.4,3.4.6q20.6,4.3,39.8-.5c5.2-4.8,9.1-10.4,13.1-16.2q1.3-1.8,2.5-3.6c11-16.1,20.8-33,30.4-49.9.8-1.3,1.5-2.6,2.3-4,20.9-36.8,38.3-75.3,53.8-114.6,1.1-2.7,2.1-5.3,3.2-8,1.9-4.7,3.7-9.4,5.5-14.1q.8-2.1,1.6-4.1c.5-1.2.9-2.5,1.4-3.8.4-1.1.8-2.1,1.2-3.2.8-3.4.6-5.3-.7-8.5q-3.8-1.9-8.3-3.1c-1.5-.5-3-.9-4.6-1.4q-3.7-1.1-6.7-1c-.5,1.4-1,2.8-1.5,4.3-10.2,28.3-20.6,56.3-33.3,83.5-.7,1.4-1.3,2.9-2,4.3q-48.7,104.1-69.8,119.6c-15.3,5.1-32.6,0-48-2.8-2.4-.4-4.9-.9-7.3-1.3-15.1-2.8-30.1-5.9-44.9-9.8q-5.1-1.4-10.6-1.2c5.2-9.7,11.5-18.7,17.8-27.8,1.1-1.6,2.2-3.3,3.4-4.9,2.7-4,5.4-7.9,8.2-11.9-4.7,0-6.3.9-10.3,3.1-1.2.7-2.4,1.3-3.6,2-1.3.7-2.6,1.4-3.9,2.1-22.3,12-45.4,21.1-70.8,13.9-5.3-1.8-10.1-4.1-15.1-6.7-1-.5-1.9-1-2.9-1.5-2-1-4.1-2.1-6.1-3.1-5-2.5-9.9-5-14.9-7.5q-1.4-.7-2.9-1.5c-1.9-1-3.9-2-5.8-2.9-2-1-4-2-6-3-2.9-1.5-5.9-3-8.8-4.4-.9-.5-1.8-.9-2.7-1.4q-4.4-2.4-9.3-3.1c.9-1.1,1.8-2.2,2.7-3.3,1.2-1.4,2.3-2.8,3.5-4.2q1.7-2.1,3.5-4.2,2.9-3.3,4.3-6.4c-.8.2-1.7.4-2.5.6-11.7,2.6-24,5-34.9-1.3-6.5-4.4-12.6-9.2-18.8-14.1-2.4-1.9-4.9-3.8-7.4-5.7-10.2-7.9-19.9-16.1-29.3-24.9-3.6-3.4-7.3-6.8-11-10.1-1.3-1.2-2.6-2.4-4-3.6-2.8-2.5-5.6-4.9-8.5-7.3q-3.8-3.2-5.2-6c1.7-.1,3.5-.3,5.3-.4,12.1-1.2,24-3.7,35.9-6.1q1.9-.4,3.9-.8c53.4-10.5,105.9-26.6,151.6-56.9.8-.5,1.6-1.1,2.5-1.6,13.7-9.2,25.8-19.6,37.5-31.3,1.8-1.8,3.7-3.6,5.6-5.4,6-5.6,10.8-11.9,15.7-18.4q1-1.4,2.1-2.8c.6-.9,1.3-1.8,1.9-2.7.6-.8,1.1-1.6,1.7-2.4,1.5-2.6,2.2-5,2.8-8-2.7-1.6-5.5-3.3-8.2-4.9-1.5-.9-3-1.8-4.6-2.7q-3.6-2.4-6.7-2.1Z"/>
                                <path class="st1" d="M620.5,11.5c10.3,6.4,19.7,14.8,23.7,26.5,2.6,17.1-3.5,31.6-9.4,47.3-1,2.7-2,5.5-3,8.2-2.1,5.9-4.3,11.7-6.5,17.6-3.5,9.3-6.8,18.6-10.2,27.9-5.2,14.5-10.5,28.9-15.8,43.4-3.6,9.7-7.1,19.5-10.7,29.3-2.3,6.3-4.6,12.6-6.9,18.9-1.1,3-2.1,5.9-3.2,8.9-1.5,4.1-3,8.2-4.5,12.3-.4,1.2-.8,2.4-1.3,3.6-3.1,8.3-6.8,16.1-10.9,24-7.8-.8-14-3.6-21-6.9-1.2-.6-2.4-1.1-3.7-1.7-3.9-1.8-7.8-3.6-11.7-5.4-3.8-1.7-7.6-3.5-11.5-5.2-2.5-1.2-5.1-2.3-7.6-3.5-6.4-2.9-12.7-5.8-19.2-8.5-1.2-.5-2.5-1.1-3.8-1.6-2.3-1-4.7-2-7-3-8.3-3.6-17.7-8.8-21.9-17.2,6.3-18.7,16.9-36,26.3-53.3,5-9.2,9.9-18.4,14.7-27.6,8.7-16.5,17.5-32.9,26.3-49.4,4.7-8.8,9.5-17.7,14.2-26.6.5-1,1.1-2,1.6-3,2.6-4.8,5.1-9.7,7.7-14.5,16.9-32,34.3-60.4,75.2-40.5ZM589.8,36.6c-10.5,19.4-20.9,38.9-31.3,58.4-10,18.9-20.1,37.8-30.2,56.7-2.7,5.1-5.5,10.2-8.2,15.4-.5.9-.9,1.7-1.4,2.6-2.2,4.1-4.4,8.3-6.7,12.4q-1.2,2.3-2.4,4.5c-.8,1.5-1.6,2.9-2.4,4.4-1.7,3.2-3.5,6.4-5.3,9.5-.5.8-.9,1.6-1.4,2.5-1.3,2.3-2.6,4.6-3.9,6.9q-2.2,3.8-.3,7.9c3.4,1.9,6.6,3.4,10.1,5q1.5.7,3.1,1.4c2.1,1,4.3,1.9,6.4,2.9,3.3,1.5,6.6,2.9,9.9,4.4,2.1.9,4.2,1.9,6.2,2.8,1,.4,2,.9,3,1.3q4.9,2.3,10.1,3.1c7.4-18.5,14.1-37.1,20.7-55.8,3-8.5,6.1-17.1,9.1-25.6q.5-1.3,1-2.7c5-13.9,10-27.7,15.1-41.5.7-1.8,1.4-3.7,2-5.5,3.3-8.8,6.5-17.6,9.8-26.4,1.2-3.2,2.4-6.4,3.6-9.6.5-1.4,1.1-2.9,1.7-4.4q3.8-10.2,5.3-21c-4.5-4.8-8.9-8-15.3-9.8-2.8,0-5.6,0-8.4,0Z"/>
                                <path class="st0" d="M142.5,263.1c15.1,14.1,25.5,32.2,26.5,53.2.7,24-4.3,44-20.4,62.6-16.6,17.2-35.3,26.1-59.3,27.4-22.2.3-42.5-7.9-58.5-23.4-1-1-2-2.1-3-3.1-.9-.9-1.8-1.7-2.7-2.6-13.4-13.9-19.4-34.1-20.1-53.1,1-23.6,10.2-43.6,27-60,.8-.8,1.5-1.6,2.3-2.4,30.2-29,78.3-24.4,108.2,1.5ZM48.8,290.9c-10.6,14.4-12.7,26.5-11.2,44.2,2.7,13.4,12.4,23.8,23,32,11.9,7.6,24.5,8.4,38.4,7.1,4.6-1.3,8.5-3.1,12.6-5.6,1.4-.8,2.8-1.6,4.3-2.4,11.2-8.9,19.4-19.7,22.2-33.9,1.3-15.9-1.5-28.1-11.6-40.6-9.4-10.4-18.6-17.1-32.8-18.5-17.7-.8-33.1,3.8-44.8,17.7Z"/>
                                <path class="st0" d="M582.6,301.4q-1.1-1-2.1-2-10.5-10-42.9-24.7c-1-.5-2-.9-3-1.4-6.6-3-13.2-6-19.9-9-3.6-1.6-7.3-3.3-10.9-4.9-6.4-2.9-12.7-5.7-19.1-8.6-2.5-1.1-5-2.2-7.5-3.3-19.4-8.7-38.5-17.8-60.2-17.6-1.3,0-2.6,0-3.8.1-8.7,0-16.6.6-25.1,2.8-12.1,3.7-22.7,9.1-32.1,16-.2,0-.4-.2-.5-.3-3.7,2.8-7.1,5.7-10.4,9-1.1,1.2-2.2,2.4-3.3,3.6-2.6,2.8-4.4,4.4-5.3,6.9-4,5.2-7.7,10.9-11,17.1-.5,1-1,2-1.6,3-2.3,4.3-4.9,8.4-4.7,13.4q1.3,2.7,4.6,4.7c1.5.6,3,1.3,4.5,1.9,1.7.7,3.4,1.5,5.2,2.2,5,2.1,9.9,4.1,14.9,6.3,1.1.5,2.1.9,3.2,1.4,11.7,5,23.2,10.1,34.7,15.4,22,10,44,19.9,66.2,29.6,17.7,7.7,35.3,15.5,52.8,23.5,18.4,8.3,36.8,16.6,55.3,24.7,2.6,1.1,5.2,2.3,7.7,3.4,3.7,1.6,7.3,3.2,11,4.9,1.1.5,2.2,1,3.4,1.5,6,2.7,11.7,5.2,18.2,6.1,6.8-17.7,13.1-35.8,13.1-54.9-.8-28.4-11.1-51.1-31.4-70.8ZM581.5,385.3q-1.8,0-4.8-1.4c-1.6-.7-3.1-1.3-4.6-2-2.7-1.2-5.4-2.3-8-3.5-1-.4-2-.9-2.9-1.3-17.7-7.7-35.3-15.6-52.8-23.5-17.9-8.1-35.8-16-53.7-23.9-18.6-8.1-37.2-16.3-55.7-24.6-2.5-1.1-5-2.3-7.5-3.4-3.6-1.6-7.1-3.2-10.7-4.8-1.1-.5-2.2-1-3.3-1.5-5.8-2.6-11.4-5.1-16.3-9.2,30.3-33.4,64.9-27.8,102.2-10.8,3.2,1.5,6.5,2.9,9.7,4.4,2.3,1,4.5,2,6.8,3,8.1,3.6,16.1,7.2,24.2,10.8,3.4,1.5,6.8,3,10.2,4.4,2.2,1,4.4,1.9,6.6,2.9,20.5,8.8,40.6,17.8,53,37.5,7.7,14.3,11.8,30.9,7.7,46.9Z"/>
                                <path class="st0" d="M179.8,167.5c-2.6-12.1-8.9-20.6-19-27.6-12.8-8.5-30.8-8.6-44.3-1.2-.1,0-.2-.1-.4-.2-3.6,2.4-4.1,2.7-5.7,4.4-.5.4-1,.8-1.4,1.2-1.8,1.9-3.3,3.7-4.6,5.5-.2.3-.4.6-.6.8-6.5,9-7.8,17.3-7.5,30.6.9,10.8,5,18.5,12.9,25.9,10.3,8.6,20.4,11.8,33.7,10.9,12.2-1.7,20.9-7.2,28.6-16.6,7.7-10.7,9.8-20.7,8.2-33.7ZM146.1,183.8c-4.7,3-7.7,3-13.2,2.2q-3.6-2-5.6-5.6c-.8-5.5-.8-8.4,2.2-13.2,4.7-3,7.7-3,13.2-2.2q3.6,2,5.6,5.6c.8,5.5.8,8.4-2.2,13.2Z"/>
                                <path class="st0" d="M297.3,251.9c-1.2-13.9-9.6-25.8-20.1-34.7-20.2-14.3-46.3-14.3-66.2-1.4-.1,0-.2-.1-.4-.2l-1.3,1.3c-3,2.1-5.9,4.5-8.5,7.2-9.7,11.6-14.1,25.3-13.9,40.3,1.3,16.9,8.1,29.5,20.9,40.4,12.1,9.5,26.2,13.4,41.5,12.2,17.5-2.6,29-11,39.7-24.6q1.2-2.2,2.4-4.4c6.6-11.5,6.6-23.3,5.8-36.3ZM266.4,267.3c-2.4,7.9-6.3,12.9-13.5,17-7,2.6-14.3,2.1-21.3-.2-6-4-10.1-8.6-12.6-15.3-1.1-6.7-.9-13,2.1-19.3,6-8.9,14.5-12.7,25.2-12.1,7,1.4,12.2,4.5,16.5,10.2,3.6,6.4,5,12.5,3.6,19.8Z"/>
                            </svg>
                        </div>
                        <span className="sr-only">Clear All Caches</span>
                    </Button>
                )}

                {/* Help Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-accent/80 transition-all duration-200"
                    title="Help & Support"
                >
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Help</span>
                </Button>

                <Separator orientation="vertical" className="h-6" />

                {/* Notifications */}
                <NotificationBell />

                {/* Admin Badge */}
                {isAdmin && (
                    <Badge variant="outline" className="hidden sm:flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200 dark:from-orange-950 dark:to-amber-950 dark:text-orange-300 dark:border-orange-800">
                        <Shield className="h-3 w-3" />
                        <span className="text-xs font-medium">Admin</span>
                    </Badge>
                )}
            </div>
        </header>
    );
}
