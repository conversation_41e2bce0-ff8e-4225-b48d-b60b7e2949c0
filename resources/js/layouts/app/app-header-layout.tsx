import { AppContent } from '@/components/app-content';
import { AppHeader } from '@/components/app-header';
import { AppShell } from '@/components/app-shell';
import { GlobalSearchCommand } from '@/components/global-search-command';
import { MobileQuickActions } from '@/components/mobile-quick-actions';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import { type BreadcrumbItem } from '@/types';
import { useAdmin } from '@/hooks/use-admin';
import type { PropsWithChildren } from 'react';

export default function AppHeaderLayout({ children, breadcrumbs }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const isAdmin = useAdmin();

    return (
        <>
            <ImpersonationBanner />
            <AppShell>
                <AppHeader breadcrumbs={breadcrumbs} />
                <AppContent>{children}</AppContent>
            </AppShell>
            <GlobalSearchCommand isAdmin={isAdmin} />
            <MobileQuickActions />
        </>
    );
}
