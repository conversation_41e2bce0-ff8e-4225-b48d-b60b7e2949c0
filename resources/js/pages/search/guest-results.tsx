import { Head, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Smartphone,
    ArrowLeft,
    Eye,
    Package,
    AlertCircle,
    CheckCircle
} from 'lucide-react';
import { AutoWatermark } from '@/components/Watermark';

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number: string;
    description: string;
    category: {
        id: number;
        name: string;
        slug?: string;
    };
    models: Array<{
        id: number;
        name: string;
        slug?: string;
        brand: {
            id: number;
            name: string;
            slug?: string;
        };
    }>;
    images?: string[];
    is_blurred?: boolean;
}

interface GuestResultsProps {
    results: {
        data: Part[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        visible_count?: number;
        blurred_count?: number;
    };
    query: string;
    guest_search_used?: boolean;
    message: string;
    signup_url: string;
    partial_results_enabled?: boolean;
    max_visible_results?: number;
    blur_intensity?: string;
    show_signup_cta?: boolean;
    is_last_search?: boolean;
    remaining_searches?: number;
    searches_used?: number;
    search_limit?: number;
}

export default function GuestResults({
    results,
    query,
    guest_search_used,
    message,
    signup_url,
    partial_results_enabled = false,
    max_visible_results = 5,
    blur_intensity = 'medium',
    show_signup_cta = true,
    is_last_search = false,
    remaining_searches = 0,
    searches_used = 0,
    search_limit = 3
}: GuestResultsProps) {
    return (
        <>
            <Head title={`Search Results for "${query}" - Mobile Parts Database`}>
                <meta name="description" content={`Found ${results.total} mobile parts matching "${query}". Sign up for unlimited access to our comprehensive database.`} />
            </Head>

            {/* Navigation */}
            <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-2">
                            <Smartphone className="h-8 w-8 text-blue-600" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">
                                FixHaat
                            </span>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Link href={route('login')}>
                                <Button variant="ghost" size="sm">
                                    Log in
                                </Button>
                            </Link>
                            <Link href={route('register')}>
                                <Button size="sm">
                                    Sign up
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    
                    {/* Back to Home */}
                    <div className="mb-6">
                        <Link href={route('home')}>
                            <Button variant="ghost" className="text-blue-600 hover:text-blue-700">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Home
                            </Button>
                        </Link>
                    </div>

                    {/* Search Info */}
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                            Search Results
                        </h1>
                        <p className="text-lg text-gray-600 dark:text-gray-300">
                            Found <span className="font-semibold">{results.total}</span> results for 
                            <span className="font-semibold"> "{query}"</span>
                        </p>
                    </div>

                    {/* Search Status Alert */}
                    {(is_last_search || guest_search_used) && (
                        <Card className="mb-8 border-orange-200 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-800">
                            <CardContent className="p-6">
                                <div className="flex items-start space-x-3">
                                    <AlertCircle className="h-6 w-6 text-orange-600 mt-0.5" />
                                    <div className="flex-1">
                                        <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-2">
                                            {is_last_search ? 'Last Free Search Used' : 'Free Search Used'}
                                        </h3>
                                        <p className="text-orange-700 dark:text-orange-300 mb-2">
                                            {message}
                                        </p>
                                        {partial_results_enabled && results.blurred_count && results.blurred_count > 0 && (
                                            <p className="text-orange-600 dark:text-orange-400 text-sm mb-4">
                                                <strong>Note:</strong> {results.visible_count} results are fully visible,
                                                {results.blurred_count} additional results are partially hidden.
                                                Sign up to see all {results.total} results clearly.
                                            </p>
                                        )}
                                        <div className="flex flex-col sm:flex-row gap-3">
                                            <Link href={signup_url}>
                                                <Button className="bg-orange-600 hover:bg-orange-700">
                                                    Sign Up for Unlimited Access
                                                </Button>
                                            </Link>
                                            <Link href={route('login')}>
                                                <Button variant="outline" className="border-orange-600 text-orange-600 hover:bg-orange-50">
                                                    Already have an account? Sign In
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Results */}
                    <div className="space-y-6">
                        {results.data.length > 0 ? (
                            <>
                                {results.data.map((part, index) => {
                                    // Show partial results separator if this is the first blurred result
                                    const showSeparator = partial_results_enabled &&
                                                        results.visible_count &&
                                                        index === results.visible_count;

                                    return (
                                        <div key={part.id}>
                                            {showSeparator && (
                                                <Card className="mb-6 border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                                                    <CardContent className="p-6 text-center">
                                                        <div className="flex items-center justify-center mb-4">
                                                            <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
                                                            <div className="px-4">
                                                                <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
                                                                    {results.blurred_count} More Results Available
                                                                </Badge>
                                                            </div>
                                                            <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
                                                        </div>
                                                        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                                                            Sign up to see all results clearly
                                                        </h3>
                                                        <p className="text-blue-700 dark:text-blue-300 mb-4">
                                                            The results below are partially hidden. Create a free account to access
                                                            all {results.total} search results with full details.
                                                        </p>
                                                        <Link href={signup_url}>
                                                            <Button className="bg-blue-600 hover:bg-blue-700">
                                                                Sign Up Now - It's Free!
                                                            </Button>
                                                        </Link>
                                                    </CardContent>
                                                </Card>
                                            )}
                                            <PartCard part={part} />
                                        </div>
                                    );
                                })}
                            </>
                        ) : (
                            <Card className="text-center py-12">
                                <CardContent>
                                    <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                        No parts found
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Try adjusting your search terms or browse our categories.
                                    </p>
                                    <Link href={route('home')}>
                                        <Button>
                                            Try Another Search
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Pagination Info */}
                    {results.total > results.per_page && (
                        <div className="mt-8 text-center">
                            <p className="text-gray-600 dark:text-gray-300">
                                Showing {results.data.length} of {results.total} results
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                Sign up to see all results and access advanced search features
                            </p>
                        </div>
                    )}

                    {/* CTA Section */}
                    <Card className="mt-12 bg-blue-600 text-white border-0">
                        <CardContent className="p-8 text-center">
                            <h2 className="text-2xl font-bold mb-4">
                                Want to see more results?
                            </h2>
                            <p className="text-blue-100 mb-6 text-lg">
                                Sign up now for unlimited searches and access to our complete database of mobile parts.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link href={signup_url}>
                                    <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                                        Start Free Trial
                                    </Button>
                                </Link>
                                <Link href={route('login')}>
                                    <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                                        Sign In
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </main>
        </>
    );
}

// Part Card Component
function PartCard({ part }: { part: Part }) {
    const isBlurred = part.is_blurred || false;
    const blurClass = isBlurred ? 'blur-sm' : '';

    return (
        <Card className="hover:shadow-lg transition-shadow relative">
            <CardContent className="p-6">
                {isBlurred && (
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/80 dark:to-gray-800/80 z-10 rounded-lg flex items-end justify-center pb-4">
                        <div className="text-center">
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Sign up to see full details
                            </p>
                            <Link href={route('register')}>
                                <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                                    View Details
                                </Button>
                            </Link>
                        </div>
                    </div>
                )}
                <div className={`flex flex-col lg:flex-row lg:items-start lg:space-x-6 ${blurClass}`}>
                    <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                                    {part.name}
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    Part #: {part.part_number}
                                </p>
                            </div>
                            <Badge variant="secondary">
                                {part.category.name}
                            </Badge>
                        </div>

                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            {part.description}
                        </p>
                        
                        {/* Compatible Models */}
                        <div className="mb-4">
                            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                                Compatible Models:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                                {part.models.slice(0, 3).map((model) => (
                                    <Badge key={model.id} variant="outline" className="text-xs">
                                        {model.brand.name} {model.name}
                                    </Badge>
                                ))}
                                {part.models.length > 3 && (
                                    <Badge variant="outline" className="text-xs">
                                        +{part.models.length - 3} more
                                    </Badge>
                                )}
                            </div>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                                <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                                Verified Compatible
                            </span>
                            <span className="flex items-center">
                                <Eye className="w-4 h-4 mr-1" />
                                View Details
                            </span>
                        </div>
                    </div>
                </div>
            </CardContent>
            <AutoWatermark />
        </Card>
    );
}
