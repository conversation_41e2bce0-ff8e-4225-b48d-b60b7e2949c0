import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Search,
    Package,
    Eye,
    Heart,
    ChevronLeft,
    ChevronRight,
    Grid,
    List,
    SlidersHorizontal
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { toast } from 'sonner';
import type { SharedData } from '@/types';
import { getCategoryIcon, getCategoryClasses } from '@/utils/category-utils';
import { AutoWatermark } from '@/components/Watermark';

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    images: string[] | null;
    is_favorited?: boolean;
    category: {
        id: number;
        name: string;
    };
    models: Array<{
        id: number;
        name: string;
        brand: {
            id: number;
            name: string;
        };
    }>;
}

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
}

interface Filters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}

interface AppliedFilters {
    category_id?: string;
    brand_id?: string;
    manufacturer?: string;
    release_year?: string;
}

interface Results {
    data: Part[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    results: Results;
    filters: Filters;
    applied_filters: AppliedFilters;
    query: string;
    remaining_searches: number;
}

export default function SearchResults({
    results,
    filters,
    applied_filters,
    query,
    remaining_searches
}: Props) {
    const { auth } = usePage<SharedData>().props;
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [showFilters, setShowFilters] = useState(false);
    const [favoritingItems, setFavoritingItems] = useState<Set<number>>(new Set());

    // Initialize favorited items from backend data
    const [favoritedItems, setFavoritedItems] = useState<Set<number>>(() => {
        if (!auth.user || !results.data) return new Set();

        const favoritedIds = results.data
            .filter((part: any) => part.is_favorited)
            .map((part: any) => part.id);

        return new Set(favoritedIds);
    });

    const handlePageChange = (page: number) => {
        const params = new URLSearchParams(window.location.search);
        params.set('page', page.toString());
        router.get(window.location.pathname + '?' + params.toString());
    };

    const handleFilterChange = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        if (value && value !== 'all') {
            params.set(key, value);
        } else {
            params.delete(key);
        }
        params.delete('page'); // Reset to first page when filtering
        router.get(window.location.pathname + '?' + params.toString());
    };

    const handleToggleFavorite = (partId: number) => {
        // Check if user is authenticated
        if (!auth.user) {
            toast.error('Please log in to manage favorites', {
                description: 'You need to be logged in to save favorites.',
            });
            return;
        }

        // Check if already processing this item
        if (favoritingItems.has(partId)) {
            return;
        }

        const isFavorited = favoritedItems.has(partId);
        setFavoritingItems(prev => new Set(prev).add(partId));

        if (isFavorited) {
            // Remove from favorites
            router.delete(route('dashboard.remove-favorite'), {
                data: {
                    type: 'part',
                    id: partId,
                }
            }, {
                onSuccess: () => {
                    setFavoritedItems(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(partId);
                        return newSet;
                    });
                    setFavoritingItems(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(partId);
                        return newSet;
                    });
                },
                onError: (errors) => {
                    setFavoritingItems(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(partId);
                        return newSet;
                    });
                }
            });
        } else {
            // Add to favorites
            router.post(route('dashboard.add-favorite'), {
                type: 'part',
                id: partId,
            }, {
                onSuccess: () => {
                    setFavoritedItems(prev => new Set(prev).add(partId));
                    setFavoritingItems(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(partId);
                        return newSet;
                    });
                },
                onError: (errors) => {
                    setFavoritingItems(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(partId);
                        return newSet;
                    });
                }
            });
        }
    };

    const PartCard = ({ part }: { part: Part }) => {
        const isLoading = favoritingItems.has(part.id);
        const isFavorited = favoritedItems.has(part.id);

        return (
            <div className="relative">
                <Card className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                            <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                {part.name}
                            </h3>
                            {part.part_number && (
                                <p className="text-sm text-gray-600 mb-1">
                                    Part #: {part.part_number}
                                </p>
                            )}
                            {part.manufacturer && (
                                <p className="text-sm text-gray-600 mb-2">
                                    by {part.manufacturer}
                                </p>
                            )}
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleFavorite(part.id)}
                            disabled={isLoading}
                            className={`${isFavorited ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'} transition-colors`}
                        >
                            <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''} ${isLoading ? 'animate-pulse' : ''}`} />
                        </Button>
                    </div>

                <div className="mb-3">
                    <div className="flex items-center gap-2 mb-2">
                        {(() => {
                            const CategoryIcon = getCategoryIcon(part.category.name);
                            return <CategoryIcon className="w-4 h-4 text-gray-600" />;
                        })()}
                        <Badge
                            variant="outline"
                            className={`${getCategoryClasses(part.category.name, 'badge')} font-medium border-2`}
                        >
                            {part.category.name}
                        </Badge>
                    </div>
                    {part.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                            {part.description}
                        </p>
                    )}
                </div>

                {part.models.length > 0 && (
                    <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Compatible with:</p>
                        <div className="flex flex-wrap gap-1">
                            {part.models.slice(0, 3).map((model) => (
                                <Badge key={model.id} variant="secondary" className="text-xs">
                                    {model.brand.name} {model.name}
                                </Badge>
                            ))}
                            {part.models.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                    +{part.models.length - 3} more
                                </Badge>
                            )}
                        </div>
                    </div>
                )}

                    <div className="flex justify-between items-center">
                        <Link href={route('parts.show', part.slug || part.id)}>
                            <Button size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                            </Button>
                        </Link>
                    </div>
                    </CardContent>
                </Card>
                <AutoWatermark />
            </div>
        );
    };

    const PartListItem = ({ part }: { part: Part }) => {
        const isLoading = favoritingItems.has(part.id);
        const isFavorited = favoritedItems.has(part.id);

        return (
            <div className="relative">
                <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <div className="flex items-start gap-4">
                                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <Package className="w-8 h-8 text-gray-400" />
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                        {part.name}
                                    </h3>
                                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                        {part.part_number && <span>Part #: {part.part_number}</span>}
                                        {part.manufacturer && <span>by {part.manufacturer}</span>}
                                        <div className="flex items-center gap-1">
                                            {(() => {
                                                const CategoryIcon = getCategoryIcon(part.category.name);
                                                return <CategoryIcon className="w-3 h-3 text-gray-500" />;
                                            })()}
                                            <Badge
                                                variant="outline"
                                                className={`${getCategoryClasses(part.category.name, 'badge')} text-xs font-medium border`}
                                            >
                                                {part.category.name}
                                            </Badge>
                                        </div>
                                    </div>
                                    {part.description && (
                                        <p className="text-sm text-gray-600 mb-2 line-clamp-1">
                                            {part.description}
                                        </p>
                                    )}
                                    {part.models.length > 0 && (
                                        <div className="flex flex-wrap gap-1">
                                            {part.models.slice(0, 5).map((model) => (
                                                <Badge key={model.id} variant="secondary" className="text-xs">
                                                    {model.brand.name} {model.name}
                                                </Badge>
                                            ))}
                                            {part.models.length > 5 && (
                                                <Badge variant="secondary" className="text-xs">
                                                    +{part.models.length - 5} more
                                                </Badge>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleToggleFavorite(part.id)}
                                disabled={isLoading}
                                className={`${isFavorited ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'} transition-colors`}
                            >
                                <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''} ${isLoading ? 'animate-pulse' : ''}`} />
                            </Button>
                            <Link href={route('parts.show', part.slug || part.id)}>
                                <Button size="sm">
                                    <Eye className="w-4 h-4 mr-2" />
                                    View Details
                                </Button>
                            </Link>
                        </div>
                    </div>
                </CardContent>
                </Card>
                <AutoWatermark />
            </div>
        );
    };

    return (
        <AppLayout>
            <Head title={`Search Results: ${query}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                Search Results
                            </h1>
                            <p className="text-gray-600">
                                {results.total} results for "{query}" 
                                {remaining_searches !== -1 && (
                                    <span className="ml-2">
                                        • {remaining_searches} searches remaining today
                                    </span>
                                )}
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant={viewMode === 'grid' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('grid')}
                            >
                                <Grid className="w-4 h-4" />
                            </Button>
                            <Button
                                variant={viewMode === 'list' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                            >
                                <List className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowFilters(!showFilters)}
                            >
                                <SlidersHorizontal className="w-4 h-4 mr-2" />
                                Filters
                            </Button>
                        </div>
                    </div>

                    {/* Filters */}
                    {showFilters && (
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle>Filters</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <Select
                                        value={applied_filters.category_id || 'all'}
                                        onValueChange={(value) => handleFilterChange('category_id', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Categories</SelectItem>
                                            {filters.categories.map((category) => (
                                                <SelectItem key={category.id} value={category.id.toString()}>
                                                    {category.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    <Select
                                        value={applied_filters.brand_id || 'all'}
                                        onValueChange={(value) => handleFilterChange('brand_id', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Brand" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Brands</SelectItem>
                                            {filters.brands.map((brand) => (
                                                <SelectItem key={brand.id} value={brand.id.toString()}>
                                                    {brand.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    <Select
                                        value={applied_filters.manufacturer || 'all'}
                                        onValueChange={(value) => handleFilterChange('manufacturer', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Manufacturer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Manufacturers</SelectItem>
                                            {filters.manufacturers?.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer: string) => (
                                                <SelectItem key={manufacturer} value={manufacturer}>
                                                    {manufacturer}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    <Select
                                        value={applied_filters.release_year || 'all'}
                                        onValueChange={(value) => handleFilterChange('release_year', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Year" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Years</SelectItem>
                                            {filters.release_years?.filter(year => year && year.toString().trim() !== '').map((year: number) => (
                                                <SelectItem key={year} value={year.toString()}>
                                                    {year}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Results */}
                    {results.data.length > 0 ? (
                        <>
                            <div className={
                                viewMode === 'grid' 
                                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'
                                    : 'space-y-4 mb-8'
                            }>
                                {results.data.map((part) => (
                                    viewMode === 'grid' 
                                        ? <PartCard key={part.id} part={part} />
                                        : <PartListItem key={part.id} part={part} />
                                ))}
                            </div>

                            {/* Pagination */}
                            {results.last_page > 1 && (
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-600">
                                        Showing {results.from} to {results.to} of {results.total} results
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(results.current_page - 1)}
                                            disabled={results.current_page === 1}
                                        >
                                            <ChevronLeft className="w-4 h-4" />
                                            Previous
                                        </Button>
                                        
                                        {Array.from({ length: Math.min(5, results.last_page) }, (_, i) => {
                                            const page = i + 1;
                                            return (
                                                <Button
                                                    key={page}
                                                    variant={page === results.current_page ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => handlePageChange(page)}
                                                >
                                                    {page}
                                                </Button>
                                            );
                                        })}
                                        
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(results.current_page + 1)}
                                            disabled={results.current_page === results.last_page}
                                        >
                                            Next
                                            <ChevronRight className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <Card>
                            <CardContent className="text-center py-12">
                                <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    No results found
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    Try adjusting your search terms or filters
                                </p>
                                <Link href={route('search.index')}>
                                    <Button>
                                        <Search className="w-4 h-4 mr-2" />
                                        New Search
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
