import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import Watermark, { AutoWatermark, useWatermarkConfig, withWatermark } from '@/components/Watermark';

// Mock fetch for API calls
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Watermark Component', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders nothing when disabled', () => {
    const { container } = render(
      <Watermark enabled={false} text="Test Watermark" />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders nothing when showForUserType is false', () => {
    const { container } = render(
      <Watermark enabled={true} showForUserType={false} text="Test Watermark" />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders text watermark when no logo URL provided', () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test Watermark" 
      />
    );
    
    expect(screen.getByText('Test Watermark')).toBeInTheDocument();
  });

  it('renders image watermark when logo URL provided', () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        logoUrl="https://example.com/logo.png"
        text="Test Watermark" 
      />
    );
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/logo.png');
    expect(image).toHaveAttribute('alt', 'Test Watermark');
  });

  it('falls back to text when image fails to load', async () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        logoUrl="https://example.com/broken-logo.png"
        text="Fallback Text" 
      />
    );
    
    const image = screen.getByRole('img');
    
    // Simulate image load error
    fireEvent.error(image);
    
    await waitFor(() => {
      expect(screen.getByText('Fallback Text')).toBeInTheDocument();
    });
  });

  it('applies correct positioning styles for top-left', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="top-left"
        offsetX={10}
        offsetY={20}
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      top: '20px',
      left: '10px'
    });
  });

  it('applies correct positioning styles for bottom-right', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="bottom-right"
        offsetX={15}
        offsetY={25}
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      bottom: '25px',
      right: '15px'
    });
  });

  it('applies correct positioning styles for center', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="center"
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    });
  });

  it('applies correct size styles for predefined sizes', () => {
    const { container: smallContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="small"
      />
    );
    
    expect(smallContainer.firstChild).toHaveStyle({
      width: '80px',
      height: '24px'
    });

    const { container: mediumContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="medium"
      />
    );
    
    expect(mediumContainer.firstChild).toHaveStyle({
      width: '120px',
      height: '36px'
    });

    const { container: largeContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="large"
      />
    );
    
    expect(largeContainer.firstChild).toHaveStyle({
      width: '160px',
      height: '48px'
    });
  });

  it('applies custom size styles', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="custom"
        customWidth={200}
        customHeight={60}
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      width: '200px',
      height: '60px'
    });
  });

  it('applies correct opacity', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        opacity={0.7}
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      opacity: '0.7'
    });
  });

  it('has correct CSS classes for pointer events and user select', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      pointerEvents: 'none',
      userSelect: 'none',
      zIndex: '10'
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        className="custom-watermark"
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-watermark');
  });
});

describe('useWatermarkConfig Hook', () => {
  it('fetches watermark configuration on mount', async () => {
    const mockConfig = {
      enabled: true,
      logo_url: 'https://example.com/logo.png',
      text: 'Test Watermark',
      position: 'bottom-right',
      opacity: 0.5,
      size: 'medium',
      custom_width: 120,
      custom_height: 40,
      offset_x: 16,
      offset_y: 16,
      show_for_user: true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockConfig,
    });

    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();
      
      if (loading) return <div>Loading...</div>;
      if (error) return <div>Error: {error}</div>;
      if (!config) return <div>No config</div>;
      
      return <div data-testid="config">{JSON.stringify(config)}</div>;
    };

    render(<TestComponent />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByTestId('config')).toBeInTheDocument();
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/watermark-config');
    expect(screen.getByTestId('config')).toHaveTextContent(JSON.stringify(mockConfig));
  });

  it('handles fetch errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();
      
      if (loading) return <div>Loading...</div>;
      if (error) return <div>Error: {error}</div>;
      
      return <div data-testid="config">{config ? 'Has config' : 'No config'}</div>;
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByText('Error: Network error')).toBeInTheDocument();
    });
  });

  it('provides default config on fetch failure', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();
      
      if (loading) return <div>Loading...</div>;
      
      return (
        <div>
          <div data-testid="error">{error || 'No error'}</div>
          <div data-testid="config">{config ? 'Has config' : 'No config'}</div>
          <div data-testid="enabled">{config?.enabled ? 'Enabled' : 'Disabled'}</div>
        </div>
      );
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('config')).toHaveTextContent('Has config');
      expect(screen.getByTestId('enabled')).toHaveTextContent('Disabled');
    });
  });
});

describe('AutoWatermark Component', () => {
  it('renders watermark with fetched configuration', async () => {
    const mockConfig = {
      enabled: true,
      logo_url: '',
      text: 'Auto Watermark',
      position: 'bottom-right',
      opacity: 0.3,
      size: 'medium',
      custom_width: 120,
      custom_height: 40,
      offset_x: 16,
      offset_y: 16,
      show_for_user: true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockConfig,
    });

    render(<AutoWatermark />);

    await waitFor(() => {
      expect(screen.getByText('Auto Watermark')).toBeInTheDocument();
    });
  });

  it('renders nothing when loading', () => {
    mockFetch.mockImplementationOnce(() => new Promise(() => {})); // Never resolves

    const { container } = render(<AutoWatermark />);
    expect(container.firstChild).toBeNull();
  });
});

describe('withWatermark HOC', () => {
  it('wraps component with watermark', async () => {
    const mockConfig = {
      enabled: true,
      logo_url: '',
      text: 'HOC Watermark',
      position: 'bottom-right',
      opacity: 0.3,
      size: 'medium',
      custom_width: 120,
      custom_height: 40,
      offset_x: 16,
      offset_y: 16,
      show_for_user: true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockConfig,
    });

    const TestComponent = ({ title }: { title: string }) => (
      <div data-testid="test-component">{title}</div>
    );

    const WatermarkedComponent = withWatermark(TestComponent);

    render(<WatermarkedComponent title="Test Title" />);

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Title')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('HOC Watermark')).toBeInTheDocument();
    });
  });

  it('applies custom watermark props', async () => {
    const mockConfig = {
      enabled: true,
      logo_url: '',
      text: 'Default Text',
      position: 'bottom-right',
      opacity: 0.3,
      size: 'medium',
      custom_width: 120,
      custom_height: 40,
      offset_x: 16,
      offset_y: 16,
      show_for_user: true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockConfig,
    });

    const TestComponent = () => <div>Test</div>;
    const WatermarkedComponent = withWatermark(TestComponent, {
      className: 'custom-watermark-class'
    });

    const { container } = render(<WatermarkedComponent />);

    await waitFor(() => {
      const watermarkContainer = container.querySelector('.custom-watermark-class');
      expect(watermarkContainer).toBeInTheDocument();
    });
  });
});
