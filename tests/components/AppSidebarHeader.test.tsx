import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AppSidebarHeader } from '@/components/app-sidebar-header';

// Mock the hooks
vi.mock('@/hooks/use-appearance', () => ({
    useAppearance: () => ({
        appearance: 'light',
        setAppearance: vi.fn(),
    }),
}));

vi.mock('@/hooks/use-admin', () => ({
    useAdmin: () => true, // Mock as admin user
}));

// Mock sonner toast
const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
};

vi.mock('sonner', () => ({
    toast: mockToast,
}));

// Mock Inertia router
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    const mockRouter = {
        post: vi.fn(),
    };

    return {
        ...actual,
        router: mockRouter,
        usePage: () => ({
            props: {
                auth: {
                    user: {
                        id: 1,
                        name: 'Admin User',
                        email: '<EMAIL>',
                        isAdmin: true,
                    },
                },
                flash: {},
            },
        }),
        Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => {
            return React.createElement('a', { ...props, href }, children);
        },
    };
});

describe('AppSidebarHeader - Clear Cache', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders clear cache button for admin users', () => {
        render(<AppSidebarHeader breadcrumbs={[]} />);
        
        const clearCacheButton = screen.getByTitle('Clear All Caches');
        expect(clearCacheButton).toBeInTheDocument();
    });

    it('calls router.post when clear cache button is clicked', async () => {
        const { router } = await import('@inertiajs/react');

        // Mock successful response
        (router.post as any).mockImplementation((url, data, options) => {
            // Simulate successful response
            if (options?.onSuccess) {
                options.onSuccess({
                    props: {
                        flash: {
                            success: 'All caches cleared successfully!'
                        }
                    }
                });
            }
            if (options?.onFinish) {
                options.onFinish();
            }
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);

        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        await waitFor(() => {
            expect(router.post).toHaveBeenCalledWith(
                '/admin/dashboard/clear-cache',
                {},
                expect.objectContaining({
                    onSuccess: expect.any(Function),
                    onError: expect.any(Function),
                    onFinish: expect.any(Function),
                })
            );
        });
    });

    it('does not show manual toast on success (relies on FlashMessageHandler)', async () => {
        // Mock successful response
        mockRouter.post.mockImplementation((url, data, options) => {
            if (options?.onSuccess) {
                options.onSuccess({
                    props: {
                        flash: {
                            success: 'All caches cleared successfully!'
                        }
                    }
                });
            }
            if (options?.onFinish) {
                options.onFinish();
            }
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);
        
        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        await waitFor(() => {
            expect(mockRouter.post).toHaveBeenCalled();
        });

        // Verify that toast.success was NOT called manually
        // (FlashMessageHandler should handle the flash message)
        expect(mockToast.success).not.toHaveBeenCalled();
    });

    it('shows error toast on client-side errors', async () => {
        // Mock error response
        mockRouter.post.mockImplementation((url, data, options) => {
            if (options?.onError) {
                options.onError({
                    message: 'Network error occurred'
                });
            }
            if (options?.onFinish) {
                options.onFinish();
            }
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);
        
        const clearCacheButton = screen.getByTitle('Clear All Caches');
        fireEvent.click(clearCacheButton);

        await waitFor(() => {
            expect(mockRouter.post).toHaveBeenCalled();
        });

        // Verify that error toast was called
        expect(mockToast.error).toHaveBeenCalledWith('Network error occurred');
    });

    it('disables button while clearing cache', async () => {
        // Mock a delayed response
        mockRouter.post.mockImplementation((url, data, options) => {
            setTimeout(() => {
                if (options?.onSuccess) {
                    options.onSuccess({
                        props: {
                            flash: {
                                success: 'All caches cleared successfully!'
                            }
                        }
                    });
                }
                if (options?.onFinish) {
                    options.onFinish();
                }
            }, 100);
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);
        
        const clearCacheButton = screen.getByTitle('Clear All Caches');
        
        // Button should be enabled initially
        expect(clearCacheButton).not.toBeDisabled();
        
        fireEvent.click(clearCacheButton);
        
        // Button should be disabled while clearing
        expect(clearCacheButton).toBeDisabled();
        
        // Wait for the operation to complete
        await waitFor(() => {
            expect(clearCacheButton).not.toBeDisabled();
        }, { timeout: 200 });
    });

    it('prevents multiple simultaneous cache clear requests', async () => {
        // Mock a delayed response
        mockRouter.post.mockImplementation((url, data, options) => {
            setTimeout(() => {
                if (options?.onFinish) {
                    options.onFinish();
                }
            }, 100);
        });

        render(<AppSidebarHeader breadcrumbs={[]} />);
        
        const clearCacheButton = screen.getByTitle('Clear All Caches');
        
        // Click multiple times rapidly
        fireEvent.click(clearCacheButton);
        fireEvent.click(clearCacheButton);
        fireEvent.click(clearCacheButton);
        
        // Should only call router.post once
        await waitFor(() => {
            expect(mockRouter.post).toHaveBeenCalledTimes(1);
        });
    });
});
